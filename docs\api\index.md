# AquaPartner API Documentation

## Introduction

Welcome to the AquaPartner API documentation. This documentation provides detailed information about the AquaPartner RESTful API endpoints, request/response formats, and usage examples.

## Base URL

All API endpoints are relative to the base URL:

```
/api
```

## Authentication

Some endpoints require authentication using Firebase authentication. For these endpoints, include the Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

## API Endpoints

The AquaPartner API is organized into the following categories:

### Customer Data

- [Customer Details](/api/customer/[id]) - Get customer details
- [Customer Address](/api/address/[id]) - Get customer address
- [User Details](/api/getUserDetails/[customerId]) - Get detailed user information

### Financial Data

- [Account Statement](/api/accountStatementFinal/[id]) - Get account statement for a customer
- [Customer Payments](/api/customerPayments/[customerId]) - Get payment transactions for a customer
- [Invoices](/api/invoices/[customerId]) - Get invoices for a customer
- [Credit Notes](/api/creditNotes/[id]) - Get credit notes for a customer

### Products and Pricing

- [Products](/api/products) - Get all available products
- [Product Catalogue](/api/productCatalogue/[id]) - Get the product catalogue with active products
- [Price List](/api/priceList/[id]) - Get the price list for products

### Schemes and Reports

- [Scheme Data](/api/scheme/[customerId]) - Get scheme data for a customer
- [Scheme with Support User Details](/api/getSchemeWithSupportUserDetails/[customerId]) - Get scheme data with support personnel details
- [SMR Report](/api/smrReport/[customerId]) - Get SMR reports for a customer
- [Liquidation By Retailer](/api/liquidationByRetailer/[customerId]) - Get liquidation data for a retailer

### UI Components

- [Carousel Data](/api/carouselData/[id]) - Get carousel data for the UI

### Payments

- [Initiate Payment](/api/initiatePayment) - Initiate a payment transaction
- [Zoho Payment](/api/zohoPayment) - Redirect to Zoho Payment Gateway

### Data Synchronization

- [Sync Invoices](/api/syncInvoices) - Synchronize invoice data with the system

### User Management

- [Users](/api/users) - Create/update a user
- [User Data](/api/users/[id]) - Get/update user data

## Detailed Documentation

For detailed documentation on specific API endpoints, please refer to the following pages:

- [Account Statement Final API](accountStatementFinal.md)
- [Invoices API](invoices.md)
- [Products API](products.md)
- [Payments API](payments.md)

## Response Format

All API responses are in JSON format. Successful responses typically include a `results` field containing the requested data:

```json
{
  "results": [
    // Data objects
  ]
}
```

Error responses include an `error` field with a description of the error:

```json
{
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

## HTTP Status Codes

The API uses standard HTTP status codes to indicate the success or failure of requests:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: The client does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

## Rate Limiting

Currently, there are no rate limits imposed on the API. However, clients should implement reasonable request rates to avoid overloading the server.

## Versioning

The current version of the API does not include version information in the URL. Future versions may include version information in the URL path or as a header.

## Support

For support with the AquaPartner API, please contact the development <NAME_EMAIL>.

## Changelog

### Version 1.0.0 (Current)

- Initial release of the AquaPartner API
- Includes endpoints for customer data, financial data, products, schemes, and payments
