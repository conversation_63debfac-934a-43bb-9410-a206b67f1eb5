import connectedDB from '@/app/config/database'
import WebhookEvent from '@/app/models/WebhookEvent'
import PaymentTransaction from '@/app/models/PaymentTransaction'
import crypto from 'crypto'

/**
 * POST /api/zoho/webhooks/test
 * Test webhook processing with sample data
 */
export async function POST(request) {
  try {
    await connectedDB()
    
    const body = await request.json()
    const { event_type = 'payment_success', create_transaction = true } = body
    
    // Create a test transaction if requested
    let testTransaction = null
    if (create_transaction) {
      testTransaction = new PaymentTransaction({
        payments_session_id: `test_session_${Date.now()}`,
        amount: 100.00,
        currency: 'INR',
        description: 'Test payment for webhook testing',
        invoice_number: `TEST-INV-${Date.now()}`,
        customer_id: 'TEST-CUSTOMER',
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+919876543210',
        status: 'created',
        meta_data: [
          { key: 'test', value: 'true' },
          { key: 'webhook_test', value: 'true' }
        ],
        session_created_time: new Date(),
        session_expires_at: new Date(Date.now() + 15 * 60 * 1000)
      })
      
      await testTransaction.save()
    }
    
    // Generate test webhook data
    const testWebhookData = generateTestWebhookData(event_type, testTransaction)
    
    // Create webhook event
    const webhookEvent = new WebhookEvent({
      event_id: `test_event_${Date.now()}`,
      event_type: event_type,
      payment_session_id: testTransaction?.payments_session_id || `test_session_${Date.now()}`,
      payment_id: testWebhookData.payment?.payment_id,
      amount: testWebhookData.payment_session?.amount || 100.00,
      currency: 'INR',
      customer_id: 'TEST-CUSTOMER',
      customer_email: '<EMAIL>',
      transaction_id: testTransaction?._id,
      invoice_number: testWebhookData.payment_session?.invoice_number,
      webhook_received_at: new Date(),
      raw_data: testWebhookData,
      signature_verified: true,
    })
    
    await webhookEvent.save()
    
    // Process the webhook
    let processingResult = null
    try {
      await processTestWebhook(webhookEvent._id)
      processingResult = { success: true, message: 'Webhook processed successfully' }
    } catch (error) {
      processingResult = { success: false, error: error.message }
    }
    
    // Get updated transaction status
    const updatedTransaction = testTransaction ? 
      await PaymentTransaction.findById(testTransaction._id) : null
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Test webhook created and processed',
        data: {
          webhook_event: {
            id: webhookEvent._id,
            event_id: webhookEvent.event_id,
            event_type: webhookEvent.event_type,
            processed: webhookEvent.processed
          },
          transaction: updatedTransaction ? {
            id: updatedTransaction._id,
            status: updatedTransaction.status,
            payments_session_id: updatedTransaction.payments_session_id
          } : null,
          processing_result: processingResult,
          test_data: testWebhookData
        }
      }),
      { status: 200 }
    )
    
  } catch (error) {
    console.error('Error creating test webhook:', error)
    return new Response(
      JSON.stringify({
        error: 'Failed to create test webhook',
        message: error.message
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/webhooks/test
 * Get test webhook examples and documentation
 */
export async function GET() {
  const testExamples = {
    message: 'Webhook Testing Endpoint',
    description: 'Use this endpoint to test webhook processing with sample data',
    usage: {
      endpoint: '/api/zoho/webhooks/test',
      method: 'POST',
      body: {
        event_type: 'payment_success | payment_failed | payment_pending | refund_success | session_expired',
        create_transaction: 'boolean (default: true)'
      }
    },
    supported_event_types: [
      {
        type: 'payment_success',
        description: 'Successful payment completion',
        result: 'Transaction status updated to "completed"'
      },
      {
        type: 'payment_failed',
        description: 'Payment failure',
        result: 'Transaction status updated to "failed"'
      },
      {
        type: 'payment_pending',
        description: 'Payment is pending',
        result: 'Transaction status updated to "pending"'
      },
      {
        type: 'refund_success',
        description: 'Successful refund',
        result: 'Transaction status updated to "refunded"'
      },
      {
        type: 'session_expired',
        description: 'Payment session expired',
        result: 'Transaction status updated to "expired"'
      }
    ],
    example_requests: [
      {
        description: 'Test successful payment',
        body: {
          event_type: 'payment_success',
          create_transaction: true
        }
      },
      {
        description: 'Test payment failure',
        body: {
          event_type: 'payment_failed',
          create_transaction: true
        }
      }
    ]
  }
  
  return new Response(JSON.stringify(testExamples), { status: 200 })
}

/**
 * Generate test webhook data based on event type
 */
function generateTestWebhookData(eventType, transaction) {
  const baseData = {
    event_type: eventType,
    timestamp: Math.floor(Date.now() / 1000),
    payment_session: {
      payments_session_id: transaction?.payments_session_id || `test_session_${Date.now()}`,
      amount: transaction?.amount || 100.00,
      currency: transaction?.currency || 'INR',
      description: transaction?.description || 'Test payment',
      invoice_number: transaction?.invoice_number || `TEST-INV-${Date.now()}`,
      created_time: Math.floor((transaction?.session_created_time?.getTime() || Date.now()) / 1000),
      meta_data: transaction?.meta_data || [
        { key: 'customer_id', value: 'TEST-CUSTOMER' },
        { key: 'test', value: 'true' }
      ]
    }
  }
  
  // Add event-specific data
  switch (eventType) {
    case 'payment_success':
    case 'payment.succeeded':
      baseData.payment = {
        payment_id: `pay_test_${Date.now()}`,
        status: 'succeeded',
        amount: baseData.payment_session.amount,
        currency: baseData.payment_session.currency,
        payment_method: 'card',
        transaction_id: `txn_test_${Date.now()}`,
        customer_email: '<EMAIL>',
        created_time: baseData.timestamp
      }
      break
      
    case 'payment_failed':
    case 'payment.failed':
      baseData.payment = {
        payment_id: `pay_test_${Date.now()}`,
        status: 'failed',
        amount: baseData.payment_session.amount,
        currency: baseData.payment_session.currency,
        error_code: 'CARD_DECLINED',
        error_message: 'Your card was declined',
        created_time: baseData.timestamp
      }
      break
      
    case 'payment_pending':
    case 'payment.pending':
      baseData.payment = {
        payment_id: `pay_test_${Date.now()}`,
        status: 'pending',
        amount: baseData.payment_session.amount,
        currency: baseData.payment_session.currency,
        payment_method: 'bank_transfer',
        created_time: baseData.timestamp
      }
      break
      
    case 'refund_success':
      baseData.refund = {
        refund_id: `ref_test_${Date.now()}`,
        payment_id: `pay_test_${Date.now()}`,
        amount: baseData.payment_session.amount,
        currency: baseData.payment_session.currency,
        status: 'succeeded',
        reason: 'Customer request',
        created_time: baseData.timestamp
      }
      break
  }
  
  return baseData
}

/**
 * Process test webhook (simplified version)
 */
async function processTestWebhook(webhookEventId) {
  const webhookEvent = await WebhookEvent.findById(webhookEventId)
  if (!webhookEvent) {
    throw new Error('Webhook event not found')
  }
  
  // Find transaction
  const transaction = await PaymentTransaction.findOne({
    payments_session_id: webhookEvent.payment_session_id
  })
  
  if (!transaction) {
    throw new Error('Transaction not found')
  }
  
  // Update transaction status
  const statusUpdates = {
    payment_success: 'completed',
    'payment.succeeded': 'completed',
    payment_failed: 'failed',
    'payment.failed': 'failed',
    payment_pending: 'pending',
    'payment.pending': 'pending',
    refund_success: 'refunded',
    session_expired: 'expired',
    'payment_session.expired': 'expired'
  }
  
  const newStatus = statusUpdates[webhookEvent.event_type]
  if (newStatus) {
    await transaction.updateStatus(newStatus, {
      payment_id: webhookEvent.payment_id,
      updated_via: 'webhook_test',
      webhook_event_id: webhookEvent._id,
      updated_at: new Date()
    })
  }
  
  await webhookEvent.markAsProcessed()
}
