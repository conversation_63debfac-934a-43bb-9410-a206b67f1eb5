import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * POST /api/zoho/auth/setup
 * Initial setup - Generate tokens from authorization code
 */
export async function POST(request) {
  try {
    const body = await request.json()
    const { authorization_code } = body

    if (!authorization_code) {
      return new Response(
        JSON.stringify({
          error: 'Authorization code is required',
          message: 'Please provide the authorization code from Zoho Developer Console',
        }),
        { status: 400 }
      )
    }

    // Validate environment variables
    if (!process.env.ZOHO_OAUTH_CLIENT_ID || !process.env.ZOHO_OAUTH_CLIENT_SECRET) {
      return new Response(
        JSON.stringify({
          error: 'OAuth configuration missing',
          message: 'ZOHO_OAUTH_CLIENT_ID and ZOHO_OAUTH_CLIENT_SECRET must be set in environment variables',
        }),
        { status: 500 }
      )
    }

    const tokenRecord = await zohoPaymentService.setupInitialTokens(authorization_code)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Zoho Payment tokens setup successfully',
        data: {
          token_id: tokenRecord._id,
          expires_at: tokenRecord.expires_at,
          scope: tokenRecord.scope,
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error in setup endpoint:', error.message)

    return new Response(
      JSON.stringify({
        error: 'Setup failed',
        message: error.message,
        details: 'Please check your authorization code and OAuth credentials',
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/auth/setup
 * Get setup instructions
 */
export async function GET() {
  const instructions = {
    message: 'Zoho Payment OAuth Setup Instructions',
    steps: [
      {
        step: 1,
        title: 'Visit Zoho Developer Console',
        url: 'https://accounts.zoho.in/developerconsole',
        description: 'Go to Zoho Developer Console and create a Self Client',
      },
      {
        step: 2,
        title: 'Generate Authorization Code',
        description: 'In the Generate Code tab, enter the required scopes and generate an authorization code',
        scopes: ['ZohoPay.payments.CREATE', 'ZohoPay.payments.READ', 'ZohoPay.refunds.CREATE', 'ZohoPay.refunds.READ'],
      },
      {
        step: 3,
        title: 'Setup Environment Variables',
        description: 'Ensure these environment variables are set:',
        variables: ['ZOHO_OAUTH_CLIENT_ID', 'ZOHO_OAUTH_CLIENT_SECRET', 'ZOHO_PAY_ACCOUNT_ID'],
      },
      {
        step: 4,
        title: 'Call Setup Endpoint',
        description: 'POST the authorization code to this endpoint',
        endpoint: '/api/zoho/auth/setup',
        payload: {
          authorization_code: 'your_authorization_code_here',
        },
      },
    ],
    current_config: {
      client_id_set: !!process.env.ZOHO_OAUTH_CLIENT_ID,
      client_secret_set: !!process.env.ZOHO_OAUTH_CLIENT_SECRET,
      account_id_set: !!process.env.ZOHO_PAY_ACCOUNT_ID,
    },
  }

  return new Response(JSON.stringify(instructions), { status: 200 })
}
