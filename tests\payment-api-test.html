<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AquaPartner Zoho Payment API Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AquaPartner Zoho Payment API Test Suite</h1>
        
        <div class="info">
            <strong>📋 Instructions:</strong>
            <ul>
                <li>This page allows you to test the Zoho Payment API integration manually</li>
                <li>Make sure your application is running on the specified base URL</li>
                <li>Use test/sandbox credentials only - do not process real payments</li>
                <li>Check the browser console for additional debug information</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>⚙️ Configuration</h3>
            <div class="form-group">
                <label for="baseUrl">Base URL:</label>
                <input type="text" id="baseUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
            </div>
            <button onclick="testHealthCheck()">🏥 Test Health Check</button>
            <button onclick="testEnvironmentVariables()">🔧 Check Environment</button>
            <div id="configResponse" class="response" style="display: none;"></div>
        </div>

        <div class="grid">
            <div class="test-section">
                <h3>💳 Legacy Payment API</h3>
                <div class="form-group">
                    <label for="legacyAmount">Amount (INR):</label>
                    <input type="number" id="legacyAmount" value="1000.50" step="0.01">
                </div>
                <div class="form-group">
                    <label for="legacyInvoiceNo">Invoice Number:</label>
                    <input type="text" id="legacyInvoiceNo" value="TEST-INV-001">
                </div>
                <div class="form-group">
                    <label for="legacyCustomerId">Customer ID:</label>
                    <input type="text" id="legacyCustomerId" value="TEST-CUST-001">
                </div>
                <div class="form-group">
                    <label for="legacyCustomerName">Customer Name:</label>
                    <input type="text" id="legacyCustomerName" value="Test Customer">
                </div>
                <div class="form-group">
                    <label for="legacyCustomerEmail">Customer Email:</label>
                    <input type="email" id="legacyCustomerEmail" value="<EMAIL>">
                </div>
                <button onclick="testLegacyPayment()">🚀 Test Legacy API</button>
                <button onclick="testLegacyPaymentInvalid()">❌ Test Invalid Data</button>
                <div id="legacyResponse" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>🆕 New Payment Session API</h3>
                <div class="form-group">
                    <label for="newAmount">Amount (INR):</label>
                    <input type="number" id="newAmount" value="1500.75" step="0.01">
                </div>
                <div class="form-group">
                    <label for="newInvoiceNumber">Invoice Number:</label>
                    <input type="text" id="newInvoiceNumber" value="TEST-INV-002">
                </div>
                <div class="form-group">
                    <label for="newCustomerId">Customer ID:</label>
                    <input type="text" id="newCustomerId" value="TEST-CUST-002">
                </div>
                <div class="form-group">
                    <label for="newDescription">Description:</label>
                    <input type="text" id="newDescription" value="Test payment for aquaculture products">
                </div>
                <button onclick="testNewPaymentSession()">🚀 Test New API</button>
                <button onclick="getAPIRequirements()">📋 Get Requirements</button>
                <div id="newResponse" class="response" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 Zoho Payment Redirect</h3>
            <div class="form-group">
                <label for="redirectAmount">Amount (INR):</label>
                <input type="number" id="redirectAmount" value="2000.00" step="0.01">
            </div>
            <div class="form-group">
                <label for="redirectInvoice">Invoice Number:</label>
                <input type="text" id="redirectInvoice" value="TEST-INV-003">
            </div>
            <button onclick="testZohoRedirect()">🔗 Test Redirect</button>
            <div id="redirectResponse" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Run All Tests</h3>
            <p>Run a comprehensive test suite covering all API endpoints and error scenarios.</p>
            <button onclick="runAllTests()">🚀 Run Complete Test Suite</button>
            <div class="loading" id="allTestsLoading">
                <div class="spinner"></div>
                <p>Running tests... Please wait.</p>
            </div>
            <div id="allTestsResponse" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        function getBaseUrl() {
            return document.getElementById('baseUrl').value.trim();
        }

        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }

        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        function showResponse(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.style.display = 'block';
            element.className = isError ? 'response error' : 'response success';
        }

        async function makeRequest(url, options = {}) {
            try {
                console.log('Making request to:', url, options);
                const response = await fetch(url, options);
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: data,
                    response: response
                };
            } catch (error) {
                console.error('Request failed:', error);
                return {
                    success: false,
                    error: error.message,
                    data: null
                };
            }
        }

        async function testHealthCheck() {
            const baseUrl = getBaseUrl();
            const result = await makeRequest(`${baseUrl}/api/zoho/health`);
            
            const responseText = `Status: ${result.status || 'Error'}\n\n${JSON.stringify(result.data || result.error, null, 2)}`;
            showResponse('configResponse', responseText, !result.success);
        }

        async function testEnvironmentVariables() {
            const baseUrl = getBaseUrl();
            const result = await makeRequest(`${baseUrl}/api/zoho/health`);
            
            if (result.success && result.data.checks && result.data.checks.environment) {
                const envCheck = result.data.checks.environment;
                const responseText = `Environment Check: ${envCheck.status}\n\n${JSON.stringify(envCheck, null, 2)}`;
                showResponse('configResponse', responseText, envCheck.status !== 'healthy');
            } else {
                showResponse('configResponse', 'Could not retrieve environment information', true);
            }
        }

        async function testLegacyPayment() {
            const baseUrl = getBaseUrl();
            const paymentData = {
                amount: parseFloat(document.getElementById('legacyAmount').value),
                invoiceNo: document.getElementById('legacyInvoiceNo').value,
                customerId: document.getElementById('legacyCustomerId').value,
                customerName: document.getElementById('legacyCustomerName').value,
                customerEmail: document.getElementById('legacyCustomerEmail').value,
                customerPhone: '+919876543210',
                redirectUrl: 'https://example.com/payment/success',
                referenceId: 'TEST-REF-001'
            };

            const result = await makeRequest(`${baseUrl}/api/initiatePayment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(paymentData)
            });

            const responseText = `Status: ${result.status || 'Error'}\n\n${JSON.stringify(result.data || result.error, null, 2)}`;
            showResponse('legacyResponse', responseText, !result.success);
        }

        async function testLegacyPaymentInvalid() {
            const baseUrl = getBaseUrl();
            const invalidData = {
                invoiceNo: 'TEST-INV-INVALID'
                // Missing required amount field
            };

            const result = await makeRequest(`${baseUrl}/api/initiatePayment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(invalidData)
            });

            const responseText = `Status: ${result.status || 'Error'}\n\n${JSON.stringify(result.data || result.error, null, 2)}`;
            showResponse('legacyResponse', responseText, result.status !== 400);
        }

        async function testNewPaymentSession() {
            const baseUrl = getBaseUrl();
            const paymentData = {
                amount: parseFloat(document.getElementById('newAmount').value),
                currency: 'INR',
                description: document.getElementById('newDescription').value,
                invoice_number: document.getElementById('newInvoiceNumber').value,
                customer_id: document.getElementById('newCustomerId').value,
                customer_name: 'Test Customer',
                customer_email: '<EMAIL>',
                customer_phone: '+919876543210',
                redirect_url: 'https://example.com/payment/success',
                reference_id: 'TEST-REF-002',
                meta_data: [
                    { key: 'product_type', value: 'aquaculture' },
                    { key: 'order_id', value: 'ORD-TEST-002' }
                ]
            };

            const result = await makeRequest(`${baseUrl}/api/zoho/payments/create-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(paymentData)
            });

            const responseText = `Status: ${result.status || 'Error'}\n\n${JSON.stringify(result.data || result.error, null, 2)}`;
            showResponse('newResponse', responseText, !result.success);
        }

        async function getAPIRequirements() {
            const baseUrl = getBaseUrl();
            const result = await makeRequest(`${baseUrl}/api/zoho/payments/create-session`);
            
            const responseText = `Status: ${result.status || 'Error'}\n\n${JSON.stringify(result.data || result.error, null, 2)}`;
            showResponse('newResponse', responseText, !result.success);
        }

        async function testZohoRedirect() {
            const baseUrl = getBaseUrl();
            const amount = document.getElementById('redirectAmount').value;
            const invoice = document.getElementById('redirectInvoice').value;
            
            const url = `${baseUrl}/api/zohoPayment?amount=${amount}&invoice=${invoice}`;
            
            try {
                const response = await fetch(url, { redirect: 'manual' });
                const responseText = `Status: ${response.status}\nRedirect Location: ${response.headers.get('location') || 'None'}\n\nThis endpoint should redirect to Zoho Payment Gateway.`;
                showResponse('redirectResponse', responseText, response.status !== 302 && response.status !== 301);
            } catch (error) {
                showResponse('redirectResponse', `Error: ${error.message}`, true);
            }
        }

        async function runAllTests() {
            showLoading('allTestsLoading');
            
            const tests = [
                { name: 'Health Check', fn: testHealthCheck },
                { name: 'Environment Variables', fn: testEnvironmentVariables },
                { name: 'Legacy Payment API', fn: testLegacyPayment },
                { name: 'Invalid Data Handling', fn: testLegacyPaymentInvalid },
                { name: 'New Payment Session API', fn: testNewPaymentSession },
                { name: 'Zoho Redirect', fn: testZohoRedirect }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    console.log(`Running test: ${test.name}`);
                    await test.fn();
                    results.push(`✅ ${test.name}: PASSED`);
                } catch (error) {
                    results.push(`❌ ${test.name}: FAILED - ${error.message}`);
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            hideLoading('allTestsLoading');
            
            const summary = `Test Suite Completed\n${'='.repeat(50)}\n\n${results.join('\n')}\n\n${'='.repeat(50)}\nTotal: ${tests.length} tests\nPassed: ${results.filter(r => r.includes('PASSED')).length}\nFailed: ${results.filter(r => r.includes('FAILED')).length}`;
            
            showResponse('allTestsResponse', summary);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AquaPartner Zoho Payment API Test Page Loaded');
            console.log('Base URL:', getBaseUrl());
        });
    </script>
</body>
</html>
