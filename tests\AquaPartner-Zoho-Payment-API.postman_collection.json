{"info": {"name": "AquaPartner Zoho Payment API", "description": "Test collection for AquaPartner Zoho Payment API integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "testAmount", "value": "1000.50", "type": "string"}, {"key": "testInvoiceNo", "value": "TEST-INV-001", "type": "string"}, {"key": "testCustomerId", "value": "TEST-CUST-001", "type": "string"}], "item": [{"name": "Environment & Health Checks", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/zoho/health", "host": ["{{baseUrl}}"], "path": ["api", "zoho", "health"]}, "description": "Check the health status of Zoho Payment API integration"}, "response": []}, {"name": "API Requirements", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/zoho/payments/create-session", "host": ["{{baseUrl}}"], "path": ["api", "zoho", "payments", "create-session"]}, "description": "Get API requirements and example payload"}, "response": []}]}, {"name": "Payment Session APIs", "item": [{"name": "Create Payment Session (New API)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": {{testAmount}},\n  \"currency\": \"INR\",\n  \"description\": \"Test payment for aquaculture products\",\n  \"invoice_number\": \"{{testInvoiceNo}}\",\n  \"customer_id\": \"{{testCustomerId}}\",\n  \"customer_name\": \"Test Customer\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+919876543210\",\n  \"redirect_url\": \"https://example.com/payment/success\",\n  \"reference_id\": \"TEST-REF-001\",\n  \"meta_data\": [\n    {\n      \"key\": \"product_type\",\n      \"value\": \"aquaculture\"\n    },\n    {\n      \"key\": \"order_id\",\n      \"value\": \"ORD-TEST-001\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/zoho/payments/create-session", "host": ["{{baseUrl}}"], "path": ["api", "zoho", "payments", "create-session"]}, "description": "Create a new payment session using the new Zoho Payment API"}, "response": []}, {"name": "Legacy Initiate Payment (Valid Data)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": {{testAmount}},\n  \"invoiceNo\": \"{{testInvoiceNo}}\",\n  \"customerId\": \"{{testCustomerId}}\",\n  \"customerName\": \"Test Customer\",\n  \"customerEmail\": \"<EMAIL>\",\n  \"customerPhone\": \"+919876543210\",\n  \"redirectUrl\": \"https://example.com/payment/success\",\n  \"referenceId\": \"TEST-REF-001\"\n}"}, "url": {"raw": "{{baseUrl}}/api/initiatePayment", "host": ["{{baseUrl}}"], "path": ["api", "initiatePayment"]}, "description": "Test the legacy initiate payment API with valid data"}, "response": []}, {"name": "Legacy Initiate Payment (Invalid Data)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceNo\": \"TEST-INV-002\"\n}"}, "url": {"raw": "{{baseUrl}}/api/initiatePayment", "host": ["{{baseUrl}}"], "path": ["api", "initiatePayment"]}, "description": "Test the legacy initiate payment API with missing required fields"}, "response": []}]}, {"name": "Payment Status & Management", "item": [{"name": "Get Payment Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/zoho/payments/status/{{paymentSessionId}}", "host": ["{{baseUrl}}"], "path": ["api", "zoho", "payments", "status", "{{paymentSessionId}}"]}, "description": "Get the status of a payment session (replace {{paymentSessionId}} with actual session ID)"}, "response": []}, {"name": "Zoho Payment Redirect", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/zohoPayment?amount={{testAmount}}&invoice={{testInvoiceNo}}", "host": ["{{baseUrl}}"], "path": ["api", "zohoPayment"], "query": [{"key": "amount", "value": "{{testAmount}}"}, {"key": "invoice", "value": "{{testInvoiceNo}}"}]}, "description": "Test the Zoho Payment redirect functionality"}, "response": []}]}, {"name": "Error <PERSON>", "item": [{"name": "Missing Amount Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceNo\": \"TEST-INV-003\",\n  \"customerId\": \"TEST-CUST-001\"\n}"}, "url": {"raw": "{{baseUrl}}/api/initiatePayment", "host": ["{{baseUrl}}"], "path": ["api", "initiatePayment"]}, "description": "Test error handling when amount field is missing"}, "response": []}, {"name": "Invalid JSON Body", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ invalid json }"}, "url": {"raw": "{{baseUrl}}/api/initiatePayment", "host": ["{{baseUrl}}"], "path": ["api", "initiatePayment"]}, "description": "Test error handling with invalid JSON body"}, "response": []}, {"name": "Empty Request Body", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/initiatePayment", "host": ["{{baseUrl}}"], "path": ["api", "initiatePayment"]}, "description": "Test error handling with empty request body"}, "response": []}]}, {"name": "Authentication Tests", "item": [{"name": "<PERSON>up <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"authorization_code\": \"YOUR_AUTHORIZATION_CODE_HERE\"\n}"}, "url": {"raw": "{{baseUrl}}/api/zoho/auth/setup", "host": ["{{baseUrl}}"], "path": ["api", "zoho", "auth", "setup"]}, "description": "Setup initial Zoho Payment tokens (replace with actual authorization code)"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic variables", "pm.collectionVariables.set('timestamp', Date.now());", "pm.collectionVariables.set('randomId', Math.random().toString(36).substring(7));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test scripts", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON', function () {", "    try {", "        pm.response.json();", "        pm.test.skip('Response is valid JSON');", "    } catch (e) {", "        pm.test('Response is not valid JSON', function () {", "            pm.expect.fail('Response is not valid JSON: ' + e.message);", "        });", "    }", "});"]}}]}