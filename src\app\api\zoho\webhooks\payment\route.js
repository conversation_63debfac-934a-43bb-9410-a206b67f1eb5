import zohoPaymentService from '@/app/lib/zohoPaymentService'
import crypto from 'crypto'

/**
 * POST /api/zoho/webhooks/payment
 * Handle payment webhooks from Zoho Payments
 */
export async function POST(request) {
  try {
    const body = await request.text()
    const signature = request.headers.get('x-zoho-webhook-signature')

    // Verify webhook signature if secret is configured
    if (process.env.ZOHO_WEBHOOK_SECRET) {
      if (!signature) {
        return new Response(
          JSON.stringify({
            error: 'Missing webhook signature',
            message: 'Webhook signature is required for verification',
          }),
          { status: 401 }
        )
      }

      const expectedSignature = crypto.createHmac('sha256', process.env.ZOHO_WEBHOOK_SECRET).update(body).digest('hex')

      if (signature !== expectedSignature) {
        console.error('Webhook signature verification failed')
        return new Response(
          JSON.stringify({
            error: 'Invalid webhook signature',
            message: 'Webhook signature verification failed',
          }),
          { status: 401 }
        )
      }
    }

    const webhookData = JSON.parse(body)
    console.log('Received webhook:', JSON.stringify(webhookData, null, 2))

    // Extract event data
    const {
      event_type,
      payment_session_id,
      payment_id,
      status,
      amount,
      currency,
      payment_method,
      created_time,
      error_code,
      error_message,
    } = webhookData

    if (!event_type || !payment_session_id) {
      return new Response(
        JSON.stringify({
          error: 'Invalid webhook data',
          message: 'event_type and payment_session_id are required',
        }),
        { status: 400 }
      )
    }

    // Get the transaction
    const transaction = await zohoPaymentService.getTransaction(payment_session_id)

    if (!transaction) {
      console.warn(`Transaction not found for payment session: ${payment_session_id}`)
      return new Response(
        JSON.stringify({
          error: 'Transaction not found',
          message: 'No transaction found for the provided payment session ID',
        }),
        { status: 404 }
      )
    }

    // Add webhook event to transaction
    await transaction.addWebhookEvent(event_type, webhookData)

    // Update transaction status based on event type
    let statusData = {}

    switch (event_type) {
      case 'payment.succeeded':
        statusData = {
          status: 'succeeded',
          payment_id,
          payment_method,
        }
        break

      case 'payment.failed':
        statusData = {
          status: 'failed',
          error_code,
          error_message,
        }
        break

      case 'payment.pending':
        statusData = {
          status: 'pending',
          payment_id,
        }
        break

      case 'payment.cancelled':
        statusData = {
          status: 'cancelled',
        }
        break

      case 'payment_session.expired':
        statusData = {
          status: 'expired',
        }
        break

      default:
        console.log(`Unhandled event type: ${event_type}`)
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Webhook received but not processed',
            event_type,
          }),
          { status: 200 }
        )
    }

    // Update transaction status
    if (Object.keys(statusData).length > 0) {
      await zohoPaymentService.updateTransactionStatus(payment_session_id, statusData)
    }

    // Log successful webhook processing
    console.log(`Webhook processed successfully: ${event_type} for session ${payment_session_id}`)

    // Send success response to Zoho
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Webhook processed successfully',
        data: {
          event_type,
          payment_session_id,
          status: statusData.status,
          processed_at: new Date().toISOString(),
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error processing webhook:', error.message)

    // Return success to prevent Zoho from retrying, but log the error
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Webhook processing failed',
        error: error.message,
        processed_at: new Date().toISOString(),
      }),
      { status: 200 } // Return 200 to prevent retries
    )
  }
}

/**
 * GET /api/zoho/webhooks/payment
 * Get webhook configuration information
 */
export async function GET() {
  const webhookInfo = {
    message: 'Zoho Payment Webhook Endpoint',
    endpoint: '/api/zoho/webhooks/payment',
    method: 'POST',
    supported_events: [
      {
        event: 'payment.succeeded',
        description: 'Payment completed successfully',
      },
      {
        event: 'payment.failed',
        description: 'Payment failed',
      },
      {
        event: 'payment.pending',
        description: 'Payment is pending',
      },
      {
        event: 'payment.cancelled',
        description: 'Payment was cancelled',
      },
      {
        event: 'payment_session.expired',
        description: 'Payment session expired',
      },
    ],
    configuration: {
      webhook_url: `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`,
      signature_verification: !!process.env.ZOHO_WEBHOOK_SECRET,
      content_type: 'application/json',
    },
    setup_instructions: [
      {
        step: 1,
        title: 'Configure Webhook URL in Zoho Payments',
        description: 'Add the webhook URL in your Zoho Payments dashboard',
      },
      {
        step: 2,
        title: 'Set Webhook Secret (Optional)',
        description: 'Set ZOHO_WEBHOOK_SECRET environment variable for signature verification',
      },
      {
        step: 3,
        title: 'Test Webhook',
        description: 'Make a test payment to verify webhook is working',
      },
    ],
  }

  return new Response(JSON.stringify(webhookInfo), { status: 200 })
}
