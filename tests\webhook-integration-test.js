/**
 * Webhook Integration Test Suite
 * 
 * Tests the Zoho Payment webhook integration functionality
 */

const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000';

/**
 * Test webhook endpoint accessibility
 */
async function testWebhookEndpoint() {
  console.log('\n=== Testing Webhook Endpoint ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/payment`);
    const data = await response.json();
    
    console.log('Webhook endpoint response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.endpoint) {
      console.log('✅ Webhook endpoint is accessible');
      return { success: true, data };
    } else {
      console.log('❌ Webhook endpoint failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Webhook endpoint request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook management endpoint
 */
async function testWebhookManagement() {
  console.log('\n=== Testing Webhook Management ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/manage`);
    const data = await response.json();
    
    console.log('Webhook management response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.statistics) {
      console.log('✅ Webhook management endpoint works');
      return { success: true, data };
    } else {
      console.log('❌ Webhook management endpoint failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Webhook management request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook with sample payment success event
 */
async function testWebhookPaymentSuccess() {
  console.log('\n=== Testing Webhook Payment Success ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event_type: 'payment_success',
        create_transaction: true
      })
    });
    
    const data = await response.json();
    console.log('Payment success test response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ Payment success webhook test passed');
      return { success: true, data };
    } else {
      console.log('❌ Payment success webhook test failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Payment success webhook test request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook with sample payment failure event
 */
async function testWebhookPaymentFailure() {
  console.log('\n=== Testing Webhook Payment Failure ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event_type: 'payment_failed',
        create_transaction: true
      })
    });
    
    const data = await response.json();
    console.log('Payment failure test response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ Payment failure webhook test passed');
      return { success: true, data };
    } else {
      console.log('❌ Payment failure webhook test failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Payment failure webhook test request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook reprocessing functionality
 */
async function testWebhookReprocessing() {
  console.log('\n=== Testing Webhook Reprocessing ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/manage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'reprocess_failed'
      })
    });
    
    const data = await response.json();
    console.log('Reprocessing test response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success !== undefined) {
      console.log('✅ Webhook reprocessing endpoint works');
      return { success: true, data };
    } else {
      console.log('❌ Webhook reprocessing failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Webhook reprocessing request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook signature verification (simulated)
 */
async function testWebhookSignatureVerification() {
  console.log('\n=== Testing Webhook Signature Verification ===');
  
  const testPayload = JSON.stringify({
    event_type: 'payment_success',
    payment_session_id: 'test_session_123',
    timestamp: Math.floor(Date.now() / 1000)
  });
  
  try {
    // Test without signature (should work if no secret configured)
    const response1 = await fetch(`${BASE_URL}/api/zoho/webhooks/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-event-id': `test_event_${Date.now()}`
      },
      body: testPayload
    });
    
    const data1 = await response1.json();
    console.log('Webhook without signature response:', JSON.stringify(data1, null, 2));
    
    // Test with invalid signature (should fail if secret configured)
    const response2 = await fetch(`${BASE_URL}/api/zoho/webhooks/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-zoho-event-id': `test_event_${Date.now()}`,
        'x-zoho-webhook-signature': 'invalid_signature'
      },
      body: testPayload
    });
    
    const data2 = await response2.json();
    console.log('Webhook with invalid signature response:', JSON.stringify(data2, null, 2));
    
    if (response1.ok) {
      console.log('✅ Webhook signature verification test completed');
      return { success: true, data: { without_signature: data1, with_invalid_signature: data2 } };
    } else {
      console.log('❌ Webhook signature verification test failed');
      return { success: false, error: { response1: data1, response2: data2 } };
    }
  } catch (error) {
    console.log('❌ Webhook signature verification test request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test webhook statistics and monitoring
 */
async function testWebhookStatistics() {
  console.log('\n=== Testing Webhook Statistics ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/webhooks/manage?limit=10`);
    const data = await response.json();
    
    console.log('Webhook statistics:', JSON.stringify(data.statistics, null, 2));
    console.log('Recent events count:', data.recent_events?.length || 0);
    
    if (response.ok && data.statistics) {
      console.log('✅ Webhook statistics endpoint works');
      return { success: true, data };
    } else {
      console.log('❌ Webhook statistics failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Webhook statistics request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Run all webhook integration tests
 */
async function runWebhookIntegrationTests() {
  console.log('🔗 Starting Webhook Integration Tests');
  console.log('=====================================');
  
  const tests = [
    { name: 'Webhook Endpoint', fn: testWebhookEndpoint },
    { name: 'Webhook Management', fn: testWebhookManagement },
    { name: 'Payment Success Webhook', fn: testWebhookPaymentSuccess },
    { name: 'Payment Failure Webhook', fn: testWebhookPaymentFailure },
    { name: 'Webhook Reprocessing', fn: testWebhookReprocessing },
    { name: 'Signature Verification', fn: testWebhookSignatureVerification },
    { name: 'Webhook Statistics', fn: testWebhookStatistics }
  ];
  
  let passed = 0;
  let failed = 0;
  const results = {};
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results[test.name] = result;
      
      if (result.success) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      results[test.name] = { success: false, error: error.message };
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n=====================================');
  console.log('🔗 WEBHOOK INTEGRATION TEST SUMMARY');
  console.log('=====================================');
  console.log(`Total Tests: ${tests.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  if (passed === tests.length) {
    console.log('\n🎉 All webhook integration tests passed!');
    console.log('✨ Webhook integration is working correctly.');
  } else {
    console.log('\n⚠️  Some webhook tests failed. Check the output above for details.');
  }
  
  return { passed, failed, total: tests.length, results };
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runWebhookIntegrationTests,
    testWebhookEndpoint,
    testWebhookManagement,
    testWebhookPaymentSuccess,
    testWebhookPaymentFailure,
    testWebhookReprocessing,
    testWebhookSignatureVerification,
    testWebhookStatistics
  };
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runWebhookIntegrationTests().catch(console.error);
}
