import connectedDB from '@/app/config/database'
import PaymentTransaction from '@/app/models/PaymentTransaction'
import ZohoPaymentToken from '@/app/models/ZohoPaymentToken'
import axios from 'axios'

class ZohoPaymentService {
  constructor() {
    this.baseURL = 'https://payments.zoho.in/api/v1'
    this.authURL = 'https://accounts.zoho.in/oauth/v2'
    this.accountId = process.env.ZOHO_PAY_ACCOUNT_ID
  }

  /**
   * Get valid access token (refresh if needed)
   */
  async getValidAccessToken() {
    await connectedDB()

    let tokenRecord = await ZohoPaymentToken.findOne({ is_active: true }).sort({ created_at: -1 })

    if (!tokenRecord) {
      throw new Error('No Zoho Payment token found. Please run initial setup.')
    }

    // Check if token needs refresh
    if (tokenRecord.needsRefresh() || tokenRecord.isExpired()) {
      console.log('Token needs refresh, refreshing...')
      tokenRecord = await this.refreshAccessToken(tokenRecord)
    }

    return tokenRecord.access_token
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(tokenRecord) {
    try {
      const response = await axios.post(`${this.authURL}/token`, null, {
        params: {
          refresh_token: tokenRecord.refresh_token,
          client_id: process.env.ZOHO_OAUTH_CLIENT_ID,
          client_secret: process.env.ZOHO_OAUTH_CLIENT_SECRET,
          grant_type: 'refresh_token',
        },
      })

      const { access_token, expires_in } = response.data

      // Calculate expiration time
      const expiresAt = new Date(Date.now() + expires_in * 1000)

      // Update token record
      tokenRecord.access_token = access_token
      tokenRecord.expires_in = expires_in
      tokenRecord.expires_at = expiresAt
      tokenRecord.updated_at = new Date()

      await tokenRecord.save()

      console.log('Access token refreshed successfully')
      return tokenRecord
    } catch (error) {
      console.error('Error refreshing access token:', error.response?.data || error.message)
      throw new Error('Failed to refresh access token')
    }
  }

  /**
   * Initial setup - Generate tokens from authorization code
   */
  async setupInitialTokens(authorizationCode) {
    try {
      await connectedDB()

      const response = await axios.post(`${this.authURL}/token`, null, {
        params: {
          code: authorizationCode,
          client_id: process.env.ZOHO_OAUTH_CLIENT_ID,
          client_secret: process.env.ZOHO_OAUTH_CLIENT_SECRET,
          grant_type: 'authorization_code',
        },
      })

      const { access_token, refresh_token, expires_in, scope } = response.data

      // Calculate expiration time
      const expiresAt = new Date(Date.now() + expires_in * 1000)

      // Deactivate old tokens
      await ZohoPaymentToken.updateMany({ is_active: true }, { is_active: false })

      // Create new token record
      const tokenRecord = new ZohoPaymentToken({
        access_token,
        refresh_token,
        expires_in,
        expires_at: expiresAt,
        scope: scope || 'ZohoPay.payments.CREATE,ZohoPay.payments.READ,ZohoPay.refunds.CREATE,ZohoPay.refunds.READ',
      })

      await tokenRecord.save()

      console.log('Initial tokens setup successfully')
      return tokenRecord
    } catch (error) {
      console.error('Error setting up initial tokens:', error.response?.data || error.message)
      throw new Error('Failed to setup initial tokens')
    }
  }

  /**
   * Create payment session
   */
  async createPaymentSession(paymentData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const {
        amount,
        currency = 'INR',
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        redirect_url,
        reference_id,
        meta_data = [],
      } = paymentData

      // Validate required fields
      if (!amount || !description || !invoice_number || !customer_id) {
        throw new Error('Missing required fields: amount, description, invoice_number, customer_id')
      }

      const sessionPayload = {
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number,
        meta_data: [
          { key: 'customer_id', value: customer_id },
          { key: 'invoice_number', value: invoice_number },
          ...meta_data,
        ],
      }

      const response = await axios.post(
        `${this.baseURL}/paymentsessions?account_id=${this.accountId}`,
        sessionPayload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      const paymentSession = response.data.payments_session

      // Save transaction to database
      await connectedDB()
      const transaction = new PaymentTransaction({
        payments_session_id: paymentSession.payments_session_id,
        amount: parseFloat(amount),
        currency,
        description,
        invoice_number,
        customer_id,
        customer_name,
        customer_email,
        customer_phone,
        status: 'created',
        meta_data: sessionPayload.meta_data,
        redirect_url,
        reference_id,
        session_created_time: new Date(paymentSession.created_time * 1000),
        session_expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      })

      await transaction.save()

      return {
        success: true,
        payment_session: paymentSession,
        transaction_id: transaction._id,
      }
    } catch (error) {
      console.error('Error creating payment session:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Retrieve payment session details
   */
  async getPaymentSession(paymentSessionId) {
    try {
      const accessToken = await this.getValidAccessToken()

      const response = await axios.get(
        `${this.baseURL}/paymentsessions/${paymentSessionId}?account_id=${this.accountId}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        }
      )

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.payments_session
    } catch (error) {
      console.error('Error retrieving payment session:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId) {
    try {
      const accessToken = await this.getValidAccessToken()

      const response = await axios.get(`${this.baseURL}/payments/${paymentId}?account_id=${this.accountId}`, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      })

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.payment
    } catch (error) {
      console.error('Error retrieving payment:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Create refund
   */
  async createRefund(paymentId, refundData) {
    try {
      const accessToken = await this.getValidAccessToken()

      const { amount, reason = 'Customer request' } = refundData

      const refundPayload = {
        amount: parseFloat(amount),
        reason,
      }

      const response = await axios.post(
        `${this.baseURL}/payments/${paymentId}/refunds?account_id=${this.accountId}`,
        refundPayload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (response.data.code !== 0) {
        throw new Error(`Zoho API Error: ${response.data.message}`)
      }

      return response.data.refund
    } catch (error) {
      console.error('Error creating refund:', error.response?.data || error.message)
      throw error
    }
  }

  /**
   * Update transaction status from webhook or manual check
   */
  async updateTransactionStatus(paymentSessionId, statusData) {
    try {
      await connectedDB()

      const transaction = await PaymentTransaction.findOne({ payments_session_id: paymentSessionId })

      if (!transaction) {
        throw new Error('Transaction not found')
      }

      await transaction.updateStatus(statusData.status, statusData)

      return transaction
    } catch (error) {
      console.error('Error updating transaction status:', error.message)
      throw error
    }
  }

  /**
   * Get transaction by payment session ID
   */
  async getTransaction(paymentSessionId) {
    try {
      await connectedDB()

      const transaction = await PaymentTransaction.findOne({ payments_session_id: paymentSessionId })

      return transaction
    } catch (error) {
      console.error('Error retrieving transaction:', error.message)
      throw error
    }
  }

  /**
   * List transactions for a customer
   */
  async getCustomerTransactions(customerId, options = {}) {
    try {
      await connectedDB()

      const { page = 1, limit = 10, status } = options
      const skip = (page - 1) * limit

      const query = { customer_id: customerId }
      if (status) {
        query.status = status
      }

      const transactions = await PaymentTransaction.find(query).sort({ created_at: -1 }).skip(skip).limit(limit)

      const total = await PaymentTransaction.countDocuments(query)

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }
    } catch (error) {
      console.error('Error retrieving customer transactions:', error.message)
      throw error
    }
  }
}

export default new ZohoPaymentService()
