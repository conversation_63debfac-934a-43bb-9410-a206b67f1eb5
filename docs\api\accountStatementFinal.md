# Account Statement Final API

## Overview

The Account Statement Final API provides detailed financial transaction history for a specific customer for the current financial year. It includes opening balance and all subsequent transactions with running balance calculations.

## Endpoint

```
GET /api/accountStatementFinal/:id
```

## Parameters

| Parameter | Type   | Required | Description                |
|-----------|--------|----------|----------------------------|
| id        | string | Yes      | The customer ID to retrieve the account statement for |

## Response

### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
{
  "results": [
    {
      "txnDate": "2023-04-01T00:00:00.000Z",
      "vchType": "Opening Balance",
      "invoiceNumber": "",
      "particulars": "***Opening Balance***",
      "debit": 0,
      "credit": 0,
      "balance": 10000
    },
    {
      "txnDate": "2023-04-15T10:30:00.000Z",
      "vchType": "Invoice",
      "invoiceNumber": "INV-001",
      "particulars": "Product Purchase",
      "debit": 5000,
      "credit": 0,
      "balance": 15000
    },
    {
      "txnDate": "2023-05-10T14:45:00.000Z",
      "vchType": "Payment",
      "invoiceNumber": "PMT-001",
      "particulars": "Payment Received",
      "debit": 0,
      "credit": 5000,
      "balance": 10000
    }
  ]
}
```

### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "Internal Server Error",
  "details": "Error message details"
}
```

## Implementation Details

The API performs the following operations:

1. Determines the current financial year (April 1st to March 31st)
2. Connects to the database
3. Retrieves the opening balance for the customer at the start of the financial year
4. Retrieves all transactions for the customer within the financial year
5. Calculates the running balance for each transaction
6. Returns the combined result with the opening balance as the first entry

## Financial Year Calculation

The financial year in India runs from April 1st to March 31st. The API automatically determines the current financial year based on the current date:

- If the current month is April (month 3 in zero-based indexing) or later, the financial year starts on April 1st of the current calendar year
- If the current month is before April, the financial year starts on April 1st of the previous calendar year

## Data Models

The API uses the following MongoDB models:

### AccountStatementFinal

Contains transaction records with the following fields:
- `customerId`: The ID of the customer
- `txnDate`: The date of the transaction
- `vchType`: The type of transaction (e.g., "Invoice", "Payment", "Credit Note")
- `invoiceNumber`: The reference number for the transaction
- `particulars`: Description of the transaction
- `debit`: The debit amount (increases the balance)
- `credit`: The credit amount (decreases the balance)

### OpeningBalance

Contains opening balance records with the following fields:
- `customerId`: The ID of the customer
- `date`: The date for the opening balance (formatted as "DD-MMM-YYYY")
- `openingBalance`: The opening balance amount

## Example Usage

### Request

```
GET /api/accountStatementFinal/CUST001
```

### Response

```json
{
  "results": [
    {
      "txnDate": "2023-04-01T00:00:00.000Z",
      "vchType": "Opening Balance",
      "invoiceNumber": "",
      "particulars": "***Opening Balance***",
      "debit": 0,
      "credit": 0,
      "balance": 25000
    },
    {
      "txnDate": "2023-04-10T09:15:00.000Z",
      "vchType": "Invoice",
      "invoiceNumber": "INV-2023-001",
      "particulars": "Purchase of Aqua Feed",
      "debit": 12000,
      "credit": 0,
      "balance": 37000
    },
    {
      "txnDate": "2023-05-05T14:30:00.000Z",
      "vchType": "Payment",
      "invoiceNumber": "PMT-2023-001",
      "particulars": "Payment via Bank Transfer",
      "debit": 0,
      "credit": 12000,
      "balance": 25000
    },
    {
      "txnDate": "2023-06-15T11:45:00.000Z",
      "vchType": "Invoice",
      "invoiceNumber": "INV-2023-015",
      "particulars": "Purchase of Aqua Chemicals",
      "debit": 8500,
      "credit": 0,
      "balance": 33500
    }
  ]
}
```

## Code Implementation

The implementation uses Next.js API routes with MongoDB for data storage. The API connects to the database, retrieves the relevant data, and calculates the running balance for each transaction.

Key features of the implementation:
- Uses moment-timezone for consistent date handling in the Indian timezone
- Calculates the financial year dynamically based on the current date
- Retrieves the opening balance for the start of the financial year
- Aggregates transaction data from the AccountStatementFinal collection
- Calculates the running balance for each transaction
- Returns a combined result with the opening balance as the first entry

## Notes

- All dates in the response are in ISO 8601 format
- The balance is calculated as a running total, with debits increasing the balance and credits decreasing it
- The opening balance entry is always the first entry in the results array
- Transactions are sorted by date in ascending order
