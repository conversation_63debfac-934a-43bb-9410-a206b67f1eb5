import connectedDB from '@/app/config/database'
import WebhookEvent from '@/app/models/WebhookEvent'
import PaymentTransaction from '@/app/models/PaymentTransaction'

/**
 * GET /api/zoho/webhooks/manage
 * Get webhook statistics and recent events
 */
export async function GET(request) {
  try {
    await connectedDB()
    
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit')) || 50
    const status = url.searchParams.get('status') // processed, unprocessed, failed
    const eventType = url.searchParams.get('event_type')
    
    // Build query
    const query = {}
    if (status === 'processed') query.processed = true
    if (status === 'unprocessed') query.processed = false
    if (status === 'failed') query.processing_error = { $exists: true }
    if (eventType) query.event_type = eventType
    
    // Get recent webhook events
    const recentEvents = await WebhookEvent.find(query)
      .sort({ webhook_received_at: -1 })
      .limit(limit)
      .select('-raw_data') // Exclude raw data for performance
    
    // Get statistics
    const stats = await WebhookEvent.aggregate([
      {
        $group: {
          _id: null,
          total_events: { $sum: 1 },
          processed_events: {
            $sum: { $cond: [{ $eq: ['$processed', true] }, 1, 0] }
          },
          failed_events: {
            $sum: { $cond: [{ $ne: ['$processing_error', null] }, 1, 0] }
          },
          avg_processing_attempts: { $avg: '$processing_attempts' }
        }
      }
    ])
    
    // Get event type breakdown
    const eventTypeStats = await WebhookEvent.aggregate([
      {
        $group: {
          _id: '$event_type',
          count: { $sum: 1 },
          processed: {
            $sum: { $cond: [{ $eq: ['$processed', true] }, 1, 0] }
          },
          failed: {
            $sum: { $cond: [{ $ne: ['$processing_error', null] }, 1, 0] }
          }
        }
      },
      { $sort: { count: -1 } }
    ])
    
    // Get recent activity (last 24 hours)
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)
    const recentActivity = await WebhookEvent.countDocuments({
      webhook_received_at: { $gte: last24Hours }
    })
    
    const response = {
      statistics: {
        total_events: stats[0]?.total_events || 0,
        processed_events: stats[0]?.processed_events || 0,
        failed_events: stats[0]?.failed_events || 0,
        pending_events: (stats[0]?.total_events || 0) - (stats[0]?.processed_events || 0),
        avg_processing_attempts: Math.round((stats[0]?.avg_processing_attempts || 0) * 100) / 100,
        recent_activity_24h: recentActivity
      },
      event_types: eventTypeStats,
      recent_events: recentEvents,
      configuration: {
        webhook_secret_configured: !!process.env.ZOHO_WEBHOOK_SECRET,
        webhook_url: `${process.env.NEXT_PUBLIC_DOMAIN}/api/zoho/webhooks/payment`
      }
    }
    
    return new Response(JSON.stringify(response), { status: 200 })
    
  } catch (error) {
    console.error('Error fetching webhook data:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to fetch webhook data',
        message: error.message 
      }),
      { status: 500 }
    )
  }
}

/**
 * POST /api/zoho/webhooks/manage
 * Reprocess failed webhooks or perform maintenance tasks
 */
export async function POST(request) {
  try {
    await connectedDB()
    
    const body = await request.json()
    const { action, webhook_event_id, event_ids } = body
    
    switch (action) {
      case 'reprocess_failed':
        return await reprocessFailedWebhooks()
        
      case 'reprocess_single':
        if (!webhook_event_id) {
          return new Response(
            JSON.stringify({ error: 'webhook_event_id is required' }),
            { status: 400 }
          )
        }
        return await reprocessSingleWebhook(webhook_event_id)
        
      case 'mark_processed':
        if (!event_ids || !Array.isArray(event_ids)) {
          return new Response(
            JSON.stringify({ error: 'event_ids array is required' }),
            { status: 400 }
          )
        }
        return await markEventsAsProcessed(event_ids)
        
      case 'cleanup_old':
        return await cleanupOldWebhooks()
        
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400 }
        )
    }
    
  } catch (error) {
    console.error('Error in webhook management:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Webhook management failed',
        message: error.message 
      }),
      { status: 500 }
    )
  }
}

/**
 * Reprocess failed webhooks
 */
async function reprocessFailedWebhooks() {
  const failedEvents = await WebhookEvent.find({
    processing_error: { $exists: true },
    processed: false,
    processing_attempts: { $lt: 3 } // Don't retry more than 3 times
  }).limit(10)
  
  let reprocessed = 0
  let errors = []
  
  for (const event of failedEvents) {
    try {
      await processWebhookEvent(event._id)
      reprocessed++
    } catch (error) {
      errors.push({
        event_id: event.event_id,
        error: error.message
      })
    }
  }
  
  return new Response(
    JSON.stringify({
      success: true,
      message: `Reprocessed ${reprocessed} failed webhooks`,
      reprocessed_count: reprocessed,
      errors: errors
    }),
    { status: 200 }
  )
}

/**
 * Reprocess single webhook
 */
async function reprocessSingleWebhook(webhookEventId) {
  try {
    const event = await WebhookEvent.findById(webhookEventId)
    if (!event) {
      return new Response(
        JSON.stringify({ error: 'Webhook event not found' }),
        { status: 404 }
      )
    }
    
    await processWebhookEvent(webhookEventId)
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Webhook reprocessed successfully',
        event_id: event.event_id
      }),
      { status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Failed to reprocess webhook',
        error: error.message
      }),
      { status: 500 }
    )
  }
}

/**
 * Mark events as processed
 */
async function markEventsAsProcessed(eventIds) {
  const result = await WebhookEvent.updateMany(
    { _id: { $in: eventIds } },
    { 
      processed: true,
      processed_at: new Date(),
      processing_error: null
    }
  )
  
  return new Response(
    JSON.stringify({
      success: true,
      message: `Marked ${result.modifiedCount} events as processed`,
      modified_count: result.modifiedCount
    }),
    { status: 200 }
  )
}

/**
 * Cleanup old webhooks (older than 30 days)
 */
async function cleanupOldWebhooks() {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  
  const result = await WebhookEvent.deleteMany({
    webhook_received_at: { $lt: thirtyDaysAgo },
    processed: true
  })
  
  return new Response(
    JSON.stringify({
      success: true,
      message: `Cleaned up ${result.deletedCount} old webhook events`,
      deleted_count: result.deletedCount
    }),
    { status: 200 }
  )
}

/**
 * Process webhook event (reused from payment route)
 */
async function processWebhookEvent(webhookEventId) {
  const webhookEvent = await WebhookEvent.findById(webhookEventId)
  if (!webhookEvent || webhookEvent.processed) {
    return
  }
  
  await webhookEvent.incrementProcessingAttempts()
  
  // Find corresponding transaction
  const transaction = await PaymentTransaction.findOne({
    payments_session_id: webhookEvent.payment_session_id
  })
  
  if (!transaction) {
    throw new Error('No transaction found for webhook')
  }
  
  // Update transaction status
  const statusUpdates = {
    payment_success: 'completed',
    'payment.succeeded': 'completed',
    payment_failed: 'failed',
    'payment.failed': 'failed',
    payment_pending: 'pending',
    'payment.pending': 'pending',
    refund_success: 'refunded',
    refund_failed: 'refund_failed',
    session_expired: 'expired',
    'payment_session.expired': 'expired',
    session_cancelled: 'cancelled',
    'payment.cancelled': 'cancelled'
  }
  
  const newStatus = statusUpdates[webhookEvent.event_type]
  if (newStatus) {
    await transaction.updateStatus(newStatus, {
      payment_id: webhookEvent.payment_id,
      updated_via: 'webhook_reprocess',
      webhook_event_id: webhookEvent._id,
      updated_at: new Date()
    })
  }
  
  await webhookEvent.markAsProcessed()
}
