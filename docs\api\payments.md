# Payment APIs

## Overview

The Payment APIs provide functionality for initiating and managing payments in the AquaPartner system. These APIs integrate with Zoho Payment Gateway for processing payments.

## Initiate Payment API

### Endpoint

```
POST /api/initiatePayment
```

### Request Body

```json
{
  "amount": 10000,
  "invoiceNo": "INV-2023-001"
}
```

| Parameter | Type   | Required | Description                |
|-----------|--------|----------|----------------------------|
| amount    | number | Yes      | The payment amount in INR  |
| invoiceNo | string | Yes      | The invoice number for reference |

### Response

#### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
{
  "payment_session_id": "ps_abc123xyz456",
  "payment_url": "https://payments.zoho.com/checkout/ps_abc123xyz456"
}
```

#### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "Internal Server Error"
}
```

### Implementation Details

The API performs the following operations:

1. Connects to the database
2. Retrieves the Zoho access token from the database
3. Makes a POST request to the Zoho Payment Session API
4. Returns the payment session ID and payment URL

### Data Models

The API uses the following MongoDB model:

#### PaymentAccessToken

Contains access token records for the Zoho Payment Gateway:
- `access_token`: The OAuth access token for Zoho API
- `refresh_token`: The refresh token for obtaining new access tokens
- `expires_at`: The expiration timestamp for the access token

### Example Usage

#### Request

```
POST /api/initiatePayment
Content-Type: application/json

{
  "amount": 11800,
  "invoiceNo": "INV-2023-001"
}
```

#### Response

```json
{
  "payment_session_id": "ps_abc123xyz456",
  "payment_url": "https://payments.zoho.com/checkout/ps_abc123xyz456"
}
```

## Zoho Payment API

### Endpoint

```
GET /api/zohoPayment
```

### Query Parameters

| Parameter | Type   | Required | Description                |
|-----------|--------|----------|----------------------------|
| amount    | number | Yes      | The payment amount in INR  |
| invoice   | string | Yes      | The invoice number for reference |

### Response

This endpoint redirects the user to the Zoho Payment Gateway.

### Implementation Details

The API performs the following operations:

1. Extracts the amount and invoice number from the query parameters
2. Redirects the user to the Zoho Payment Gateway with the appropriate parameters

### Example Usage

```
GET /api/zohoPayment?amount=11800&invoice=INV-2023-001
```

## Customer Payments API

### Endpoint

```
GET /api/customerPayments/:customerId
```

### Parameters

| Parameter   | Type   | Required | Description                |
|-------------|--------|----------|----------------------------|
| customerId  | string | Yes      | The customer ID to retrieve payments for |

### Response

#### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
{
  "results": [
    {
      "paymentsDate": "15 Aug 2023",
      "amount": 11800,
      "paymentMode": "Online",
      "referenceNumber": "TXN123456",
      "invoiceNumber": "INV-2023-001"
    }
  ]
}
```

#### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "Internal Server Error"
}
```

### Implementation Details

The API performs the following operations:

1. Connects to the database
2. Retrieves payment transactions for the specified customer
3. Sorts the payments by date in descending order (newest first)
4. Returns the sorted payments

### Data Models

The API uses the following MongoDB model:

#### CustomerPayments (Transactions)

Contains payment transaction records with the following fields:
- `customerId`: The ID of the customer
- `paymentsDate`: The date of the payment (format: "DD MMM YYYY")
- `amount`: The payment amount
- `paymentMode`: The mode of payment (e.g., "Online", "Cheque", "Cash")
- `referenceNumber`: The reference number for the transaction
- `invoiceNumber`: The invoice number associated with the payment

### Example Usage

#### Request

```
GET /api/customerPayments/CUST001
```

#### Response

```json
{
  "results": [
    {
      "paymentsDate": "20 Aug 2023",
      "amount": 17700,
      "paymentMode": "Online",
      "referenceNumber": "TXN789012",
      "invoiceNumber": "INV-2023-025"
    },
    {
      "paymentsDate": "15 Aug 2023",
      "amount": 11800,
      "paymentMode": "Online",
      "referenceNumber": "TXN123456",
      "invoiceNumber": "INV-2023-001"
    }
  ]
}
```

## Integration with Zoho Payment Gateway

The Payment APIs integrate with the Zoho Payment Gateway for processing payments. The integration involves the following steps:

1. Obtaining an access token from Zoho using OAuth 2.0
2. Creating a payment session with the Zoho Payment Session API
3. Redirecting the user to the Zoho Payment Gateway for completing the payment
4. Handling the payment callback from Zoho

### Environment Variables

The following environment variables are required for the Zoho Payment Gateway integration:

- `ZOHO_PAYMENT_SESSION_URL`: The URL for creating a payment session
- `ZOHO_PAY_ACCOUNT_ID`: The Zoho Pay account ID

### Security Considerations

- Access tokens are stored securely in the database
- All communication with the Zoho Payment Gateway is done over HTTPS
- Sensitive payment information is handled directly by the Zoho Payment Gateway, not by the AquaPartner system
