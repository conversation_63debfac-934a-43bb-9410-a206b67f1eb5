#!/usr/bin/env node

/**
 * Test Runner for Zoho Payment API Integration
 * 
 * This script runs comprehensive tests for the Zoho Payment API integration.
 * It can be executed from the command line to validate the API functionality.
 * 
 * Usage:
 *   node tests/run-payment-tests.js
 *   npm run test:payments
 */

const fs = require('fs');
const path = require('path');

// Load environment variables (optional)
try {
  require('dotenv').config({ path: '.env.local' });
} catch (e) {
  console.log('Note: dotenv not available, using system environment variables');
}

// Import the test functions
const {
  runAllTests,
  testEnvironmentVariables,
  testHealthCheck,
  testLegacyInitiatePayment,
  testInvalidDataHandling,
  testNewPaymentSessionAPI,
  testZohoPaymentGET,
  testDatabaseConnection
} = require('./zoho-payment-api-tests');

/**
 * Enhanced test runner with detailed reporting
 */
async function runEnhancedTests() {
  console.log('🚀 AquaPartner Zoho Payment API Test Suite');
  console.log('===========================================');
  console.log(`📅 Test Run: ${new Date().toISOString()}`);
  console.log(`🌐 Base URL: ${process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'}`);
  console.log('===========================================\n');

  const startTime = Date.now();
  const testResults = {};

  try {
    // Run individual test suites with detailed reporting
    console.log('🔧 Phase 1: Environment & Configuration Tests');
    testResults.environmentVariables = await testEnvironmentVariables();
    
    console.log('\n🏥 Phase 2: Health Check Tests');
    testResults.healthCheck = await testHealthCheck();
    testResults.databaseConnection = await testDatabaseConnection();
    
    console.log('\n💳 Phase 3: Payment API Tests');
    testResults.legacyInitiatePayment = await testLegacyInitiatePayment();
    testResults.newPaymentSessionAPI = await testNewPaymentSessionAPI();
    testResults.zohoPaymentGET = await testZohoPaymentGET();
    
    console.log('\n🛡️ Phase 4: Error Handling Tests');
    testResults.invalidDataHandling = await testInvalidDataHandling();

    // Generate detailed report
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    generateDetailedReport(testResults, duration);
    
    // Save results to file
    await saveTestResults(testResults, duration);
    
    return testResults;
    
  } catch (error) {
    console.error('❌ Test suite failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

/**
 * Generate detailed test report
 */
function generateDetailedReport(testResults, duration) {
  console.log('\n===========================================');
  console.log('📊 DETAILED TEST REPORT');
  console.log('===========================================');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  
  const categories = {
    'Configuration': ['environmentVariables'],
    'Health & Database': ['healthCheck', 'databaseConnection'],
    'Payment APIs': ['legacyInitiatePayment', 'newPaymentSessionAPI', 'zohoPaymentGET'],
    'Error Handling': ['invalidDataHandling']
  };
  
  for (const [category, tests] of Object.entries(categories)) {
    console.log(`\n📋 ${category}:`);
    
    for (const testName of tests) {
      const result = testResults[testName];
      if (result) {
        totalTests++;
        const status = result.success ? '✅ PASSED' : '❌ FAILED';
        const details = result.success ? '' : ` (${result.error || 'Unknown error'})`;
        console.log(`  ${testName}: ${status}${details}`);
        
        if (result.success) {
          passedTests++;
        } else {
          failedTests++;
        }
      }
    }
  }
  
  console.log('\n===========================================');
  console.log('📈 SUMMARY STATISTICS');
  console.log('===========================================');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)`);
  console.log(`Failed: ${failedTests} (${((failedTests / totalTests) * 100).toFixed(1)}%)`);
  console.log(`Duration: ${(duration / 1000).toFixed(2)} seconds`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! 🎉');
    console.log('✨ Zoho Payment API integration is working correctly.');
  } else {
    console.log('\n⚠️  SOME TESTS FAILED');
    console.log('🔧 Please check the configuration and error details above.');
  }
  
  console.log('\n===========================================');
}

/**
 * Save test results to file
 */
async function saveTestResults(testResults, duration) {
  const reportData = {
    timestamp: new Date().toISOString(),
    duration: duration,
    summary: {
      total: Object.keys(testResults).length,
      passed: Object.values(testResults).filter(r => r.success).length,
      failed: Object.values(testResults).filter(r => !r.success).length
    },
    results: testResults,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      baseUrl: process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000'
    }
  };
  
  const reportsDir = path.join(__dirname, 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportsDir, `payment-api-test-${timestamp}.json`);
  
  try {
    fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
    console.log(`📄 Test report saved to: ${reportFile}`);
  } catch (error) {
    console.warn(`⚠️  Could not save test report: ${error.message}`);
  }
}

/**
 * Check if the application is running
 */
async function checkApplicationStatus() {
  const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000';
  
  try {
    const response = await fetch(`${baseUrl}/api/health`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Main execution
 */
async function main() {
  // Check if application is running
  console.log('🔍 Checking if application is running...');
  const isRunning = await checkApplicationStatus();
  
  if (!isRunning) {
    console.log('⚠️  Application does not appear to be running.');
    console.log('💡 Please start the application with: npm run dev');
    console.log('🌐 Then access it at: http://localhost:3000');
    process.exit(1);
  }
  
  console.log('✅ Application is running and accessible.\n');
  
  // Run the test suite
  const results = await runEnhancedTests();
  
  // Exit with appropriate code
  const allPassed = Object.values(results).every(r => r.success);
  process.exit(allPassed ? 0 : 1);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Run the main function if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  runEnhancedTests,
  generateDetailedReport,
  saveTestResults,
  checkApplicationStatus
};
